import React, { useEffect, useState, useRef } from "react";
import { isEmpty } from "lodash";
import parse from "html-react-parser";
import { FaGift, FaSearch } from "react-icons/fa"; // Gift icon and Search icon
import { Dialog } from "primereact/dialog";
import { Dropdown } from "primereact/dropdown";
import { InputNumber } from "primereact/inputnumber";
import { Button } from "primereact/button";
import { Toast } from "primereact/toast";
import axios from "axios";
import { Tooltip } from "primereact/tooltip";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import CreateCardToManagerForm from "../../Backages/CreateCardToManagerForm";

// Add these imports for the animated SVGs
import { motion } from "framer-motion";
import { HiOutlineCreditCard, HiOutlineCash } from "react-icons/hi";
import { BsBank2 } from "react-icons/bs";

// Import the context hook
import { useDataTableContext } from "../../../../contexts/UsersDataTableContext";
import { useGlobalContext } from "../../../../contexts/GlobalContext";
import { useQueryParams } from "../../../../utils/helper";
import { useDeleteUserMutation } from "../../../../quires/user";
import { usersTableConfig, defaultTableConfig } from "../../../../constants";
import { useLayout } from "../../../../contexts/LayoutContext";

import TemplateImageHeader from "./TemplateFilter";
import AssignGroupDialog from "../Groups/AssignGroupDialog";
import AddMemberDialog from "./AddMemberDialog";
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";
import Element from "../../DesignSpace/components/Element";

const statusStyles = {
  printed: "bg-[#22C55E] ",
  unprinted: "bg-[#64748B] ",
  onProgress: "bg-[#D97706] ",
};

function ManagersDataTable() {
  const {
    totalRecords,
    lazyManagersParams,
    setLazyManagersParams,
    data,
    dataHandler,
    loading: tableLoading,  // تغيير اسم المتغير هنا
  } = useDataTableContext();
  const { dialogHandler, openDialog, selectedMembers, setSelectedMembers } =
    useGlobalContext();
  const { isMobile } = useLayout();

  const queryParams = useQueryParams();
  const groupID = queryParams.get("group-id");
  const designID = queryParams.get("design-id");
  const deleteRow = useDeleteUserMutation();

  // Search state
  const [searchQuery, setSearchQuery] = useState('');

  const [selectedMember, setSelectedMember] = useState();
  const [actionType, setActionType] = useState("create"); // create or update
  const [giftDialogVisible, setGiftDialogVisible] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [availablePackages, setAvailablePackages] = useState([]);
  const [giftLoading, setGiftLoading] = useState(false);  // تغيير اسم المتغير هنا
  const [formData, setFormData] = useState({
    user_id: null,
    package_id: null,
    duration: 12,
    payment_method: "gift",
    total_price: 0
  });
  const toastRef = useRef(null);  // تغيير من useState إلى useRef

  // Add state for animation
  const [animationKey, setAnimationKey] = useState(0);

  useEffect(() => {
    setLazyManagersParams({
      ...defaultTableConfig,
      ...usersTableConfig,
      groupID: groupID,
      designID: designID,
    });
  }, []);

  // Add debounced search handler
  useEffect(() => {
    const timeout = setTimeout(() => {
      setLazyManagersParams(prev => ({
        ...prev,
        filters: {
          ...prev.filters,
          name: { value: searchQuery, matchMode: 'contains' }
        }
      }));
    }, 300);

    return () => clearTimeout(timeout);
  }, [searchQuery, setLazyManagersParams]);

  const createMember = () => {
    setActionType("create");
    setSelectedMember({});
    dialogHandler("addMember");
  };

  const createGroup = () => {
    setSelectedMember({});
    dialogHandler("createGroup");
  };

  const editMember = (data) => {
    setActionType("update");
    const updatedData = { ...data };
    delete updatedData.role;
    delete updatedData.group_permission;
    setSelectedMember(updatedData);
    dialogHandler("addMember");
  };

  const deleteRowHandler = async (rowData) => {
    await deleteRow.mutateAsync(
      { id: rowData?.id },
      {
        onSuccess: () => {
          setLazyManagersParams((prev) => ({ ...prev, ...usersTableConfig }));
        },
      }
    );
  };

  const fetchAvailablePackages = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get(
        `${import.meta.env.VITE_BACKEND_URL}/packages/original_packages`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      setAvailablePackages(response.data || []);
    } catch (error) {
      console.error("Error fetching packages:", error);
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to fetch available packages",
        life: 3000
      });
    }
  };

  // تقديم نموذج الهدية
  const handleGiftSubmit = async () => {
    try {
      setGiftLoading(true);
      const token = localStorage.getItem("token");

      // For bank_transfer, we'll use the manually entered total_price directly
      // No need to recalculate it here

      await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/admin/assign-package-to-user`,
        {
          user_id: formData.user_id,
          package_id: formData.package_id,
          duration: formData.duration,
          payment_method: formData.payment_method,
          total_price: formData.payment_method === "bank_transfer" ? formData.total_price : 0
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json"
          }
        }
      );

      toastRef.current.show({
        severity: "success",
        summary: "Success",
        detail: "Package has been successfully assigned to the user",
        life: 3000
      });

      setGiftDialogVisible(false);
      // Update data
      setLazyManagersParams((prev) => ({ ...prev, ...usersTableConfig }));
    } catch (error) {
      console.error("Error assigning package:", error);
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: error.response?.data?.message || "An error occurred while assigning the package",
        life: 3000
      });
    } finally {
      setGiftLoading(false);
    }
  };

  const openGiftDialog = (rowData) => {
    setFormData({
      ...formData,
      user_id: rowData.id,
      package_id: null,
      duration: 12,
      payment_method: "gift"
    });
    fetchAvailablePackages();
    setGiftDialogVisible(true);
  };

  const actionBodyTemplate = (rowData) => {
    const currentUserId = localStorage.getItem("user_id");
    return (
      <>
        <div className="d-inline-block text-nowrap">

          <Tooltip
            target={`.gift-button-${rowData.id}`}
            showDelay={100}
            className="fs-8"
          />
          <button
            className={`main-btn text-md shadow-md gift-button-${rowData.id} me-3`}
            data-pr-position="bottom"
            data-pr-tooltip="Gift Package"
            onClick={() => openGiftDialog(rowData)}
          >
            <FaGift className="text-white" />
          </button>

          <Tooltip
            target={`.create-card-button-${rowData.id}`}
            showDelay={100}
            className="fs-8"
          />
          <button
            className={`main-btn text-md shadow-md create-card-button-${rowData.id} me-3`}
            data-pr-position="bottom"
            data-pr-tooltip="Create Card"
            onClick={() => {
              setSelectedMember(rowData);
              dialogHandler("CreateCardToManagerForm", true);
            }}
          >
            <HiOutlineCreditCard />
          </button>

          <Tooltip
            target={`.update-button-${rowData.id}`}
            showDelay={100}
            className="fs-8"
          />
          <button
            className={`btn btn-sm btn-icon update-button-${rowData.id} me-3`}
            data-pr-position="bottom"
            data-pr-tooltip="Update"
            onClick={() => editMember(rowData)}
          >
            <FiEdit />
          </button>

          {rowData.id.toString() !== currentUserId && (
            <>
              <Tooltip
                target={`.delete-button-${rowData.id}`}
                showDelay={100}
                className="fs-8"
              />
              <button
                className={`btn btn-sm btn-icon delete-button-${rowData.id}`}
                data-pr-position="bottom"
                data-pr-tooltip="Delete"
                onClick={() => deleteRowHandler(rowData)}
              >
                <TfiTrash />
              </button>
            </>
          )}
        </div>
      </>
    );
  };

  const selectAllHandler = (rowsData) => {
    setSelectedMembers((prev) => ({ ...prev, data: rowsData }));
  };

  const imageBodyTemplate = (rowData) => {
    if (rowData?.tempate_image_html) {
      return (
        <div className="border border-black rounded-md ">
          {parse(rowData?.tempate_image_html)}
        </div>
      );
    }
    if (rowData?.design) {
      const scaleFactor = 1;
      const elements = JSON.parse(rowData?.design?.init_template);
      return (
        <div
          style={{
            width: `${240 * scaleFactor}px`,
            height: `${416 * scaleFactor}px`,
            borderRadius: "5px",
            border: "1px solid black",
            position: "relative",
          }}
        >
          {elements.map((el) => (
            <div
              key={el.id}
              style={{
                background: "transparent",
                position: "absolute",
                top: el.y * scaleFactor,
                left: el.x * scaleFactor,
                width: el.width * scaleFactor,
                height: el.height * scaleFactor,
              }}
            >
              <Element el={el} scaleFactor={scaleFactor} userData={rowData} />
            </div>
          ))}
        </div>
      );
    }
  };

  const profileBodyTemplate = (rowData) => {
    return rowData?.image ? (
      <img loading="lazy" src={rowData?.image} width={100} alt="profile" />
    ) : (
      ""
    );
  };
  const statusBodyTemplate = (rowData) => {
    if (!rowData?.packages || rowData?.packages.length === 0) {
      return (
        <span className="text-[white] rounded-[6px] font-bold text-sm py-2 px-3 capitalize bg-[#6B7280] min-w-[100px] inline-block text-center">
          No package
        </span>
      );
    }

    const lastPackage = rowData.packages[rowData.packages.length - 1];

    const statusClass =
      lastPackage?.status === "active" ? "bg-[#22C55E]" : "bg-[#DC2626]";

    return (
      <span
        className={`text-[white] rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusClass} min-w-[100px] inline-block text-center`}
      >
        {lastPackage?.status}
      </span>
    );
  };

  const Header = () => {
    const activeBtn = isEmpty(selectedMembers.data);
    const userRole = localStorage.getItem("user_role");
    return (
      <div className="w-full">
        <div className="flex justify-between mb-4">
          <TemplateImageHeader />
          <div>
            {userRole !== "user" && (
              <button
                className="main-btn text-md shadow-md"
                onClick={() => createMember()}
              >
                Add Manager
              </button>
            )}
          </div>
        </div>

        {/* Search Bar Section */}
        <div className={`w-full mb-4 mt-1 ${isMobile ? 'px-2' : 'flex justify-center items-center'}`}>
          <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[700px]'}`}>
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder="Search by manager name..."
              className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                        focus:outline-none focus:ring-2 focus:ring-blue-300
                        focus:border-blue-300 transition-all duration-200"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <Toast ref={toastRef} />
      <Tooltip target=".email-tooltip" position="top" appendTo="body" />
      <div className="w-full mt-8">
        <div className=" flex-grow h-full">
          <DataTable
            selection={selectedMembers.data}
            onSelectionChange={(e) => selectAllHandler(e.value)}
            lazy
            // filterDisplay="row"
            header={Header}
            responsiveLayout="stack"
            breakpoint="960px"
            dataKey="id"
            paginator
            className="table w-full border h-full"
            value={data}
            first={lazyManagersParams?.first}
            rows={lazyManagersParams?.rows}
            rowsPerPageOptions={[5, 25, 50, 100]}
            totalRecords={totalRecords}
            onPage={dataHandler}
            onSort={dataHandler}
            sortField={lazyManagersParams?.sortField}
            sortOrder={lazyManagersParams?.sortOrder}
            onFilter={dataHandler}
            filters={lazyManagersParams?.filters}
            loading={tableLoading}
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
            scrollable
            scrollHeight="100%"
          >
            <Column
              selectionMode="multiple"
              headerStyle={{ width: "3rem" }}
              style={{ width: "1rem" }}
              // bodyStyle={{ padding: '0' }}
              headerClassName="no-padding"
              exportable={false}
            />
            <Column
            body={profileBodyTemplate}
            header="Profile Image"
            className="text-center"
            style={{ width: '5rem' }}
            bodyStyle={{ padding: '0' }}
            headerClassName="no-padding"
            />
            {/* <Column
              body={imageBodyTemplate}
              header="Template Image"
              className="text-center"
            /> */}
            <Column
              field="name"
              header="Name"
              className="text-center"
              filter
              sortable
              style={{ minWidth: '1rem' }}
            />
            {/* <Column
              field="user_type"
              header="Role"
              className="text-center"
              filter
              sortable
              style={{ width: '12rem' }}
              body={(rowData) =>
                rowData.user_type ? rowData.user_type : "null"
              }
            /> */}
            <Column
              field="phone"
              header="Phone"
              className="text-center"
              filter
              sortable
              showFilterMenu={false}
            />
              <Column
                  field="email"
                  header="Email"
                  className="text-center"
                  filter
                  sortable
                  style={{ width: '2rem' }}
                  showFilterMenu={false}
                  body={(rowData) => {
                    const email = rowData.email || '';
                    return (
                      <div className="email-tooltip" data-pr-tooltip={email}>
                        {email.length > 15 ? (
                          <>
                            {email.substring(0, 15)}
                            <span style={{ color: '#666' }}>...</span>
                          </>
                        ) : (
                          email
                        )}
                      </div>

                    );
                  }}
                />
            <Column
              field="company_name"
              header="company_name"
              className="text-center"
              filter
              sortable
              showFilterMenu={false}
            />
            {/* <Column
              field="department"
              header="Department"
              className="text-center"
              filter
              sortable
              showFilterMenu={false}
            /> */}
            <Column
              field="status"
              body={statusBodyTemplate}
              header="Package Status"
              className="text-center"
              showFilterMenu={false}
              filter
              sortable
            />
            <Column
              body={actionBodyTemplate}
              exportable={false}
              style={{ minWidth: "8rem" }}
            />
          </DataTable>
        </div>


        <Dialog
          visible={giftDialogVisible}
          style={{ width: "650px" }} // Increased from 500px to 650px
          header="Assign Package to User"
          modal
          className="p-fluid"
          footer={
            <div className="flex justify-between w-full pt-3"> {/* Changed from justify-end to justify-between */}
              <Button
                label="Cancel"
                icon="pi pi-times"
                onClick={() => setGiftDialogVisible(false)}
                className="p-button-danger p-button-rounded"
              />
              <Button
                label="Assign Package"
                icon="pi pi-gift"
                onClick={handleGiftSubmit}
                loading={giftLoading}
                className="p-button-success p-button-rounded"
              />
            </div>
          }
          onHide={() => setGiftDialogVisible(false)}
        >
          <div className="gift-form p-4">
            {/* Payment method icon animation */}
            <div className="flex justify-center mb-4">
              <motion.div
                key={animationKey}
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, type: "spring", bounce: 0.4 }}
                className="p-6 rounded-full bg-gray-100" // Increased padding
              >
                {formData.payment_method === "gift" ? (
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 10, 0] }}
                    transition={{ duration: 1, repeat: Infinity, repeatType: "loop", ease: "easeInOut" }}
                  >
                    <FaGift size={80} className="text-purple-500" /> {/* Increased from 50 to 80 */}
                  </motion.div>
                ) : (
                  <motion.div
                    animate={{ y: [0, -5, 0] }}
                    transition={{ duration: 1, repeat: Infinity, repeatType: "loop", ease: "easeInOut" }}
                  >
                    <BsBank2 size={80} className="text-blue-500" /> {/* Increased from 50 to 80 */}
                  </motion.div>
                )}
              </motion.div>
            </div>

            <div className="field mb-4">
              <label htmlFor="package" className="block text-sm font-medium mb-2">
                Select Package
              </label>
              <Dropdown
                id="package"
                value={formData.package_id}
                options={availablePackages.map(pkg => ({
                  label: `${pkg.name} (${pkg.card_limit} cards)`,
                  value: pkg.id
                }))}
                onChange={(e) => {
                  const selectedPkg = availablePackages.find(p => p.id === e.value);
                  setFormData({
                    ...formData,
                    package_id: e.value,
                    total_price: formData.payment_method === "bank_transfer" ?
                      (formData.duration === 12 ? selectedPkg?.yearly_price : selectedPkg?.monthly_price) : 0
                  });
                  setSelectedPackage(selectedPkg);
                }}
                placeholder="Select a package"
                className="w-full"
                disabled={giftLoading}
                required
                optionLabel="label"
              />
              {selectedPackage && (
                <div className="mt-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Monthly Price:</span>
                    <span>${selectedPackage.monthly_price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Yearly Price:</span>
                    <span>${selectedPackage.yearly_price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Card Types:</span>
                    <span>{selectedPackage.card_type_names.join(', ')}</span>
                  </div>
                </div>
              )}
            </div>

            <div className="field mb-4">
              <label htmlFor="duration" className="block text-sm font-medium mb-2">
                Subscription Duration
              </label>
              <Dropdown
                id="duration"
                value={formData.duration}
                options={[
                  { label: "Monthly (1 month)", value: 1 },
                  { label: "Quarterly (3 months)", value: 3 },
                  { label: "Semi-Annual (6 months)", value: 6 },
                  { label: "Annual (12 months)", value: 12 }
                ]}
                onChange={(e) => {
                  // Update duration
                  setFormData(prev => ({ ...prev, duration: e.value }));

                  // Suggest a price if package is selected and payment method is bank transfer
                  if (selectedPackage && formData.payment_method === "bank_transfer") {
                    let suggestedPrice;

                    if (e.value === 12) {
                      suggestedPrice = selectedPackage.yearly_price;
                    } else if (e.value === 6) {
                      suggestedPrice = (selectedPackage.monthly_price * 6) * 0.9; // 10% discount
                    } else if (e.value === 3) {
                      suggestedPrice = (selectedPackage.monthly_price * 3) * 0.95; // 5% discount
                    } else {
                      suggestedPrice = selectedPackage.monthly_price;
                    }

                    // Suggest the price but don't force it - user can still edit manually
                    setFormData(prev => ({
                      ...prev,
                      duration: e.value,
                      total_price: suggestedPrice
                    }));
                  }
                }}
                placeholder="Select duration"
                className="w-full"
                disabled={giftLoading}
              />
            </div>

            <div className="field mb-4">
              <label htmlFor="payment_method" className="block text-sm font-medium mb-2">
                Payment Method
              </label>
              <Dropdown
                id="payment_method"
                value={formData.payment_method}
                options={[
                  { label: "Gift (Free)", value: "gift" },
                  { label: "Bank Transfer", value: "bank_transfer" }
                ]}
                onChange={(e) => {
                  setFormData({ ...formData, payment_method: e.value });
                  // Trigger animation when payment method changes
                  setAnimationKey(prev => prev + 1);
                }}
                className="w-full"
                disabled={giftLoading}
              />
            </div>

            {formData.payment_method === "bank_transfer" && (
              <div className="field mb-4">
                <label htmlFor="total_price" className="block text-sm font-medium mb-2">
                  Total Price
                </label>
                <InputNumber
                  id="total_price"
                  value={formData.total_price}
                  onValueChange={(e) => setFormData({ ...formData, total_price: e.value })}
                  mode="currency"
                  currency="USD"
                  locale="en-US"
                  className="w-full"
                  disabled={giftLoading}
                  required
                />
                <small className="text-blue-600 mt-1 block">
                  You can adjust this price manually if needed.
                </small>
              </div>
            )}

            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                <i className="pi pi-info-circle mr-2"></i>
                {formData.payment_method === "gift"
                  ? "This will provide a free package to the user."
                  : "This will assign a paid package via bank transfer."}
              </p>
            </div>
          </div>
        </Dialog>

        {openDialog?.addMember && (
          <AddMemberDialog data={selectedMember} actionType={actionType} />
        )}
        {openDialog?.createGroup && (
          <AssignGroupDialog data={selectedMembers.data} />
        )}
        {openDialog?.updateGroup && <AssignGroupDialog />}

        {openDialog?.CreateCardToManagerForm &&
          selectedMember &&
          selectedMember.packages && (
            <CreateCardToManagerForm
              userId={selectedMember.id}
              packages={selectedMember.packages.filter(
                (pkg) => pkg.status === "active"
              )}
            />
          )}
      </div>
    </>
  );
}

export default ManagersDataTable;
